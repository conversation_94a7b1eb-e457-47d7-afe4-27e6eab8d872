# Test info

- Name: User Story 1: Add Website Monitoring >> can save monitoring configuration
- Location: C:\Users\<USER>\Documents\augment-projects\uptime tracker\tests\test_add_website.spec.js:92:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('form[data-testid="add-website-form"]')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('form[data-testid="add-website-form"]')

    at C:\Users\<USER>\Documents\augment-projects\uptime tracker\tests\test_add_website.spec.js:98:24
```

# Page snapshot

```yaml
- banner:
  - heading "Website Monitoring" [level=1]
- main:
  - heading "Lisa uus veebisait monitooringusse" [level=2]
  - paragraph: Siin tuleb vorm veebisaidi lisamiseks...
- contentinfo:
  - paragraph: © 2025 Uptime Tracker
```

# Test source

```ts
   1 | /**
   2 |  * Playwright tests for website monitoring functionality - TDD approach
   3 |  * Testing ALL acceptance criteria for User Story 1: "Veebisaidi monitooringu lisamine"
   4 |  *
   5 |  * Acceptance Criteria:
   6 |  * 1. Saan sisestada veebisaidi URL-i (nt. https://example.com)
   7 |  * 2. Saan määrata kontrolli intervalli (1 min, 5 min, 15 min, 30 min, 1 tund)
   8 |  * 3. Saan anda saidile nime/kirjelduse
   9 |  * 4. Süsteem valideerib URL-i formaadi õigsust
   10 |  * 5. Saan salvestada monitooringu konfiguratsiooni
   11 |  * 6. Kuvatakse kinnitus monitooringu edukast lisamisest
   12 |  */
   13 |
   14 | const { test, expect } = require('@playwright/test');
   15 |
   16 | test.describe('User Story 1: Add Website Monitoring', () => {
   17 |
   18 |   // Criterion 1: Saan sisestada veebisaidi URL-i (nt. https://example.com)
   19 |   test('can input website URL', async ({ page }) => {
   20 |     await page.goto('/');
   21 |
   22 |     const urlInput = page.locator('input[data-testid="url-input"]');
   23 |     await expect(urlInput).toBeVisible();
   24 |     await expect(urlInput).toHaveAttribute('type', 'url');
   25 |     await expect(urlInput).toHaveAttribute('placeholder', 'Enter website URL (e.g., https://example.com)');
   26 |
   27 |     const testUrl = 'https://example.com';
   28 |     await urlInput.fill(testUrl);
   29 |     await expect(urlInput).toHaveValue(testUrl);
   30 |   });
   31 |
   32 |   // Criterion 2: Saan määrata kontrolli intervalli (1 min, 5 min, 15 min, 30 min, 1 tund)
   33 |   test('can select monitoring interval', async ({ page }) => {
   34 |     await page.goto('/');
   35 |
   36 |     const intervalSelect = page.locator('select[data-testid="interval-select"]');
   37 |     await expect(intervalSelect).toBeVisible();
   38 |
   39 |     // Check all required interval options exist
   40 |     await expect(intervalSelect.locator('option[value="1"]')).toHaveText('1 minut');
   41 |     await expect(intervalSelect.locator('option[value="5"]')).toHaveText('5 minutit');
   42 |     await expect(intervalSelect.locator('option[value="15"]')).toHaveText('15 minutit');
   43 |     await expect(intervalSelect.locator('option[value="30"]')).toHaveText('30 minutit');
   44 |     await expect(intervalSelect.locator('option[value="60"]')).toHaveText('1 tund');
   45 |
   46 |     // Test selecting an interval
   47 |     await intervalSelect.selectOption('5');
   48 |     await expect(intervalSelect).toHaveValue('5');
   49 |   });
   50 |
   51 |   // Criterion 3: Saan anda saidile nime/kirjelduse
   52 |   test('can input website name and description', async ({ page }) => {
   53 |     await page.goto('/');
   54 |
   55 |     const nameInput = page.locator('input[data-testid="website-name"]');
   56 |     await expect(nameInput).toBeVisible();
   57 |     await expect(nameInput).toHaveAttribute('placeholder', 'Website name (e.g., My Blog)');
   58 |
   59 |     const descriptionInput = page.locator('textarea[data-testid="website-description"]');
   60 |     await expect(descriptionInput).toBeVisible();
   61 |     await expect(descriptionInput).toHaveAttribute('placeholder', 'Optional description');
   62 |
   63 |     // Test inputting name and description
   64 |     await nameInput.fill('Test Website');
   65 |     await descriptionInput.fill('This is a test website for monitoring');
   66 |
   67 |     await expect(nameInput).toHaveValue('Test Website');
   68 |     await expect(descriptionInput).toHaveValue('This is a test website for monitoring');
   69 |   });
   70 |
   71 |   // Criterion 4: Süsteem valideerib URL-i formaadi õigsust
   72 |   test('validates URL format', async ({ page }) => {
   73 |     await page.goto('/');
   74 |
   75 |     const urlInput = page.locator('input[data-testid="url-input"]');
   76 |     const submitButton = page.locator('button[data-testid="add-website-btn"]');
   77 |     const errorMessage = page.locator('[data-testid="url-error"]');
   78 |
   79 |     // Test invalid URL
   80 |     await urlInput.fill('invalid-url');
   81 |     await submitButton.click();
   82 |
   83 |     await expect(errorMessage).toBeVisible();
   84 |     await expect(errorMessage).toHaveText('Palun sisestage kehtiv URL (nt. https://example.com)');
   85 |
   86 |     // Test valid URL should not show error
   87 |     await urlInput.fill('https://example.com');
   88 |     await expect(errorMessage).not.toBeVisible();
   89 |   });
   90 |
   91 |   // Criterion 5: Saan salvestada monitooringu konfiguratsiooni
   92 |   test('can save monitoring configuration', async ({ page }) => {
   93 |     await page.goto('/');
   94 |
   95 |     const form = page.locator('form[data-testid="add-website-form"]');
   96 |     const submitButton = page.locator('button[data-testid="add-website-btn"]');
   97 |
>  98 |     await expect(form).toBeVisible();
      |                        ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   99 |     await expect(submitButton).toBeVisible();
  100 |     await expect(submitButton).toHaveText('Lisa monitooringusse');
  101 |
  102 |     // Fill out the form
  103 |     await page.locator('input[data-testid="url-input"]').fill('https://example.com');
  104 |     await page.locator('input[data-testid="website-name"]').fill('Example Site');
  105 |     await page.locator('select[data-testid="interval-select"]').selectOption('5');
  106 |
  107 |     // Submit the form
  108 |     await submitButton.click();
  109 |
  110 |     // Should redirect or show success (will fail in RED phase)
  111 |     await expect(page).toHaveURL(/.*\/success|.*\/dashboard/);
  112 |   });
  113 |
  114 |   // Criterion 6: Kuvatakse kinnitus monitooringu edukast lisamisest
  115 |   test('shows confirmation after successful monitoring setup', async ({ page }) => {
  116 |     await page.goto('/');
  117 |
  118 |     // Fill and submit form
  119 |     await page.locator('input[data-testid="url-input"]').fill('https://example.com');
  120 |     await page.locator('input[data-testid="website-name"]').fill('Example Site');
  121 |     await page.locator('select[data-testid="interval-select"]').selectOption('5');
  122 |     await page.locator('button[data-testid="add-website-btn"]').click();
  123 |
  124 |     // Check for success message
  125 |     const successMessage = page.locator('[data-testid="success-message"]');
  126 |     await expect(successMessage).toBeVisible();
  127 |     await expect(successMessage).toContainText('Monitooring edukalt lisatud');
  128 |     await expect(successMessage).toContainText('Example Site');
  129 |     await expect(successMessage).toContainText('https://example.com');
  130 |   });
  131 |
  132 |   // Additional test: Complete form validation
  133 |   test('form requires all mandatory fields', async ({ page }) => {
  134 |     await page.goto('/');
  135 |
  136 |     const submitButton = page.locator('button[data-testid="add-website-btn"]');
  137 |
  138 |     // Try to submit empty form
  139 |     await submitButton.click();
  140 |
  141 |     // Should show validation errors
  142 |     const urlError = page.locator('[data-testid="url-required-error"]');
  143 |     const nameError = page.locator('[data-testid="name-required-error"]');
  144 |
  145 |     await expect(urlError).toBeVisible();
  146 |     await expect(urlError).toHaveText('URL on kohustuslik');
  147 |     await expect(nameError).toBeVisible();
  148 |     await expect(nameError).toHaveText('Veebisaidi nimi on kohustuslik');
  149 |   });
  150 |
  151 | });
  152 |
```