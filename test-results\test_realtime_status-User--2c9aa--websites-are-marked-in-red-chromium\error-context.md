# Test info

- Name: User Story 2: Realtime Status Monitoring >> offline websites are marked in red
- Location: C:\Users\<USER>\Documents\augment-projects\uptime tracker\tests\test_realtime_status.spec.js:111:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('[data-testid="website-item"][data-status="offline"]').first()
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('[data-testid="website-item"][data-status="offline"]').first()

    at C:\Users\<USER>\Documents\augment-projects\uptime tracker\tests\test_realtime_status.spec.js:116:34
```

# Page snapshot

```yaml
- text: "Error: Failed to lookup view \"dashboard\" in views directory \"C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\views\" at Function.render (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\application.js:597:17) at ServerResponse.render (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\response.js:1049:7) at C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\server.js:77:7 at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\router\\layer.js:95:5) at next (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\router\\route.js:149:13) at Route.dispatch (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\router\\route.js:119:3) at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\router\\layer.js:95:5) at C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\router\\index.js:284:15 at Function.process_params (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\router\\index.js:346:12) at next (C:\\Users\\<USER>\\Documents\\augment-projects\\uptime tracker\\node_modules\\express\\lib\\router\\index.js:280:10)"
```

# Test source

```ts
   16 |
   17 | test.describe('User Story 2: Realtime Status Monitoring', () => {
   18 |   
   19 |   // Criterion 1: Kuvatakse kõik monitooritavad saidid loendina
   20 |   test('displays all monitored websites in a list', async ({ page }) => {
   21 |     await page.goto('/dashboard');
   22 |     
   23 |     // Check that the websites list container exists
   24 |     const websitesList = page.locator('[data-testid="websites-list"]');
   25 |     await expect(websitesList).toBeVisible();
   26 |     
   27 |     // Check that individual website items are displayed
   28 |     const websiteItems = page.locator('[data-testid="website-item"]');
   29 |     await expect(websiteItems).toHaveCount(3); // Assuming 3 test websites
   30 |     
   31 |     // Check that each website item contains required information
   32 |     const firstWebsite = websiteItems.first();
   33 |     await expect(firstWebsite).toBeVisible();
   34 |     await expect(firstWebsite.locator('[data-testid="website-name"]')).toBeVisible();
   35 |     await expect(firstWebsite.locator('[data-testid="website-url"]')).toBeVisible();
   36 |   });
   37 |
   38 |   // Criterion 2: Iga saidi juures on nähtav praegune staatus (ONLINE/OFFLINE/UNKNOWN)
   39 |   test('shows current status for each website', async ({ page }) => {
   40 |     await page.goto('/dashboard');
   41 |     
   42 |     const websiteItems = page.locator('[data-testid="website-item"]');
   43 |     
   44 |     // Check first website has status
   45 |     const firstWebsite = websiteItems.first();
   46 |     const statusElement = firstWebsite.locator('[data-testid="website-status"]');
   47 |     await expect(statusElement).toBeVisible();
   48 |     
   49 |     // Status should be one of the valid values
   50 |     const statusText = await statusElement.textContent();
   51 |     expect(['ONLINE', 'OFFLINE', 'UNKNOWN']).toContain(statusText);
   52 |     
   53 |     // Check that status has appropriate data attribute for styling
   54 |     await expect(statusElement).toHaveAttribute('data-status', /^(online|offline|unknown)$/);
   55 |   });
   56 |
   57 |   // Criterion 3: Kuvatakse viimase kontrolli aeg
   58 |   test('displays last check time for each website', async ({ page }) => {
   59 |     await page.goto('/dashboard');
   60 |     
   61 |     const websiteItems = page.locator('[data-testid="website-item"]');
   62 |     const firstWebsite = websiteItems.first();
   63 |     
   64 |     const lastCheckElement = firstWebsite.locator('[data-testid="last-check-time"]');
   65 |     await expect(lastCheckElement).toBeVisible();
   66 |     
   67 |     // Should display time in readable format
   68 |     const timeText = await lastCheckElement.textContent();
   69 |     expect(timeText).toMatch(/\d{2}:\d{2}:\d{2}|\d+ minutit tagasi|Mitte kunagi/);
   70 |     
   71 |     // Should have proper label
   72 |     await expect(lastCheckElement).toContainText('Viimane kontroll:');
   73 |   });
   74 |
   75 |   // Criterion 4: Kuvatakse vastuse aeg millisekundites
   76 |   test('displays response time in milliseconds', async ({ page }) => {
   77 |     await page.goto('/dashboard');
   78 |     
   79 |     const websiteItems = page.locator('[data-testid="website-item"]');
   80 |     const firstWebsite = websiteItems.first();
   81 |     
   82 |     const responseTimeElement = firstWebsite.locator('[data-testid="response-time"]');
   83 |     await expect(responseTimeElement).toBeVisible();
   84 |     
   85 |     // Should display response time with ms unit
   86 |     const responseText = await responseTimeElement.textContent();
   87 |     expect(responseText).toMatch(/\d+\s*ms|N\/A/);
   88 |     
   89 |     // Should have proper label
   90 |     await expect(responseTimeElement).toContainText('Vastuse aeg:');
   91 |   });
   92 |
   93 |   // Criterion 5: Online saidid on roheliselt märgitud
   94 |   test('online websites are marked in green', async ({ page }) => {
   95 |     await page.goto('/dashboard');
   96 |     
   97 |     // Find an online website (assuming test data exists)
   98 |     const onlineWebsite = page.locator('[data-testid="website-item"][data-status="online"]').first();
   99 |     await expect(onlineWebsite).toBeVisible();
  100 |     
  101 |     // Check that the status element has green styling
  102 |     const statusElement = onlineWebsite.locator('[data-testid="website-status"]');
  103 |     await expect(statusElement).toHaveClass(/.*green.*|.*online.*|.*success.*/);
  104 |     
  105 |     // Check computed style (green color)
  106 |     const statusColor = await statusElement.evaluate(el => getComputedStyle(el).color);
  107 |     expect(statusColor).toMatch(/rgb\(.*0.*255.*0.*\)|green|#.*0.*f.*0.*/i);
  108 |   });
  109 |
  110 |   // Criterion 6: Offline saidid on punaselt märgitud
  111 |   test('offline websites are marked in red', async ({ page }) => {
  112 |     await page.goto('/dashboard');
  113 |     
  114 |     // Find an offline website (assuming test data exists)
  115 |     const offlineWebsite = page.locator('[data-testid="website-item"][data-status="offline"]').first();
> 116 |     await expect(offlineWebsite).toBeVisible();
      |                                  ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  117 |     
  118 |     // Check that the status element has red styling
  119 |     const statusElement = offlineWebsite.locator('[data-testid="website-status"]');
  120 |     await expect(statusElement).toHaveClass(/.*red.*|.*offline.*|.*error.*|.*danger.*/);
  121 |     
  122 |     // Check computed style (red color)
  123 |     const statusColor = await statusElement.evaluate(el => getComputedStyle(el).color);
  124 |     expect(statusColor).toMatch(/rgb\(255.*0.*0.*\)|red|#.*f.*0.*0.*/i);
  125 |   });
  126 |
  127 |   // Criterion 7: Leht uueneb automaatselt iga 30 sekundi järel
  128 |   test('page auto-refreshes every 30 seconds', async ({ page }) => {
  129 |     await page.goto('/dashboard');
  130 |     
  131 |     // Check that auto-refresh is enabled
  132 |     const autoRefreshIndicator = page.locator('[data-testid="auto-refresh-indicator"]');
  133 |     await expect(autoRefreshIndicator).toBeVisible();
  134 |     await expect(autoRefreshIndicator).toContainText('Automaatne uuendamine: 30s');
  135 |     
  136 |     // Check for refresh timer countdown
  137 |     const refreshTimer = page.locator('[data-testid="refresh-timer"]');
  138 |     await expect(refreshTimer).toBeVisible();
  139 |     
  140 |     // Wait a bit and check that timer is counting down
  141 |     const initialTime = await refreshTimer.textContent();
  142 |     await page.waitForTimeout(2000); // Wait 2 seconds
  143 |     const laterTime = await refreshTimer.textContent();
  144 |     
  145 |     expect(parseInt(laterTime)).toBeLessThan(parseInt(initialTime));
  146 |   });
  147 |
  148 |   // Additional test: Dashboard navigation and layout
  149 |   test('dashboard has proper navigation and layout', async ({ page }) => {
  150 |     await page.goto('/dashboard');
  151 |     
  152 |     // Check page title
  153 |     await expect(page).toHaveTitle(/.*Dashboard.*|.*Monitooring.*/);
  154 |     
  155 |     // Check main heading
  156 |     const heading = page.locator('h1, h2').first();
  157 |     await expect(heading).toContainText('Monitooritavad veebisaidid');
  158 |     
  159 |     // Check navigation back to add website
  160 |     const addWebsiteLink = page.locator('[data-testid="add-website-link"]');
  161 |     await expect(addWebsiteLink).toBeVisible();
  162 |     await expect(addWebsiteLink).toHaveText('Lisa uus veebisait');
  163 |   });
  164 |
  165 |   // Additional test: Empty state when no websites
  166 |   test('shows empty state when no websites are monitored', async ({ page }) => {
  167 |     // Navigate to dashboard with no websites (assuming clean state)
  168 |     await page.goto('/dashboard?empty=true');
  169 |     
  170 |     const emptyState = page.locator('[data-testid="empty-state"]');
  171 |     await expect(emptyState).toBeVisible();
  172 |     await expect(emptyState).toContainText('Pole veel ühtegi veebisaiti lisatud');
  173 |     
  174 |     const addFirstWebsiteButton = page.locator('[data-testid="add-first-website-btn"]');
  175 |     await expect(addFirstWebsiteButton).toBeVisible();
  176 |     await expect(addFirstWebsiteButton).toHaveText('Lisa esimene veebisait');
  177 |   });
  178 |
  179 | });
  180 |
```