"""
Test for website monitoring functionality - TDD approach
Testing the first acceptance criterion: User can input website URL
"""

import unittest
from website_monitor import WebsiteMonitor


class TestWebsiteMonitor(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.monitor = WebsiteMonitor()
    
    def test_can_input_website_url(self):
        """
        Test that user can input a website URL.
        First acceptance criterion: <PERSON><PERSON> si<PERSON>a veeb<PERSON>idi URL-i (nt. https://example.com)
        """
        # Arrange
        test_url = "https://example.com"
        
        # Act
        result = self.monitor.add_url(test_url)
        
        # Assert
        self.assertTrue(result, "Should be able to add a valid URL")
        self.assertIn(test_url, self.monitor.get_urls(), "URL should be stored in the monitor")


if __name__ == '__main__':
    unittest.main()
