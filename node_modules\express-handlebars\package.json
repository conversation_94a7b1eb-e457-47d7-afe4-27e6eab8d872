{"name": "express-handlebars", "description": "A Handlebars view engine for Express which doesn't suck.", "version": "7.1.3", "homepage": "https://github.com/express-handlebars/express-handlebars", "keywords": ["express", "express3", "handlebars", "view", "layout", "partials", "templates"], "author": "<PERSON> <<EMAIL>> (http://ericf.me/)", "repository": {"type": "git", "url": "git://github.com/express-handlebars/express-handlebars.git"}, "bugs": {"url": "https://github.com/express-handlebars/express-handlebars/issues"}, "engines": {"node": ">=v16"}, "dependencies": {"glob": "^10.4.2", "graceful-fs": "^4.2.11", "handlebars": "^4.7.8"}, "main": "dist/index.js", "directories": {"example": "examples"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.0.6", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.0", "@types/glob": "^8.1.0", "@types/jest": "^29.5.12", "@types/node": "^18.19.32", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.2.0", "jest-cli": "^29.7.0", "semantic-release": "^24.0.0", "ts-jest": "^29.1.5", "typescript": "^5.4.5"}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}, "scripts": {"test": "jest --verbose", "test:cover": "jest --coverage", "lint": "eslint .", "build": "tsc"}, "license": "BSD-3-<PERSON><PERSON>"}