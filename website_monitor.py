"""
Website Monitor - Minimal implementation for TDD
"""


class WebsiteMonitor:
    """
    A class to monitor website uptime and availability.
    """
    
    def __init__(self):
        """Initialize the website monitor."""
        pass
    
    def add_url(self, url):
        """
        Add a URL to monitor.
        
        Args:
            url (str): The URL to monitor
            
        Returns:
            bool: True if URL was added successfully, False otherwise
        """
        # Minimal implementation - will be expanded based on test requirements
        pass
    
    def get_urls(self):
        """
        Get list of monitored URLs.
        
        Returns:
            list: List of monitored URLs
        """
        # Minimal implementation - will be expanded based on test requirements
        pass
