# Test info

- Name: Add Website Monitoring >> form has submit button
- Location: C:\Users\<USER>\Documents\augment-projects\uptime tracker\tests\test_add_website.spec.js:40:3

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('button[data-testid="add-website-btn"]')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('button[data-testid="add-website-btn"]')

    at C:\Users\<USER>\Documents\augment-projects\uptime tracker\tests\test_add_website.spec.js:46:32
```

# Page snapshot

```yaml
- banner:
  - heading "Website Monitoring" [level=1]
- main:
  - heading "Lisa uus veebisait monitooringusse" [level=2]
  - paragraph: Siin tuleb vorm veebisaidi lisamiseks...
- contentinfo:
  - paragraph: © 2025 Uptime Tracker
```

# Test source

```ts
   1 | /**
   2 |  * Playwright test for website monitoring functionality - TDD approach
   3 |  * Testing the first acceptance criterion: User can input website URL
   4 |  */
   5 |
   6 | const { test, expect } = require('@playwright/test');
   7 |
   8 | test.describe('Add Website Monitoring', () => {
   9 |   
  10 |   test('URL input field exists and is visible', async ({ page }) => {
  11 |     // Navigate to the application
  12 |     await page.goto('/');
  13 |     
  14 |     // Check that URL input field exists
  15 |     const urlInput = page.locator('input[data-testid="url-input"]');
  16 |     await expect(urlInput).toBeVisible();
  17 |     
  18 |     // Check that input has appropriate placeholder
  19 |     await expect(urlInput).toHaveAttribute('placeholder', 'Enter website URL (e.g., https://example.com)');
  20 |     
  21 |     // Check that input has correct type
  22 |     await expect(urlInput).toHaveAttribute('type', 'url');
  23 |   });
  24 |
  25 |   test('can input website URL', async ({ page }) => {
  26 |     // Navigate to the application
  27 |     await page.goto('/');
  28 |     
  29 |     // Find the URL input field
  30 |     const urlInput = page.locator('input[data-testid="url-input"]');
  31 |     
  32 |     // Input a test URL
  33 |     const testUrl = 'https://example.com';
  34 |     await urlInput.fill(testUrl);
  35 |     
  36 |     // Verify the URL was entered correctly
  37 |     await expect(urlInput).toHaveValue(testUrl);
  38 |   });
  39 |
  40 |   test('form has submit button', async ({ page }) => {
  41 |     // Navigate to the application
  42 |     await page.goto('/');
  43 |     
  44 |     // Check that submit button exists
  45 |     const submitButton = page.locator('button[data-testid="add-website-btn"]');
> 46 |     await expect(submitButton).toBeVisible();
     |                                ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  47 |     await expect(submitButton).toHaveText('Lisa monitooringusse');
  48 |   });
  49 |
  50 | });
  51 |
```